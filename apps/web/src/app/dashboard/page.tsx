"use client";

import { useUser } from "@clerk/nextjs";
import {
  ExternalLink,
  Lightbulb,
  Loader2,
  Plus,
  Send,
  Settings,
  Trash2,
} from "lucide-react";
import { redirect } from "next/navigation";
import type React from "react";
import { useState } from "react";
import { toast } from "sonner";
import Authenticated<PERSON>avbar from "@/components/authenticated-navbar";

// Type definitions for sync result
interface SyncResult {
  success: boolean;
  newMentions?: number;
  limitReached?: boolean;
  currentCount?: number;
  maxAllowed?: number;
  error?: string;
}

// Type definition for API errors
interface APIError {
  message?: string;
  code?: string;
  data?: unknown;
  shape?: unknown;
}

// Type definition for monitored account
interface MonitoredAccount {
  id: string;
  avatar: string | null;
  createdAt: string;
  displayName: string | null;
  isActive: boolean;
  handle: string;
  followerCount: number;
  platform: string;
  lastSyncAt: string | null;
  mentionsCount: number;
  syncMentions?: boolean;
  syncUserTweets?: boolean;
  syncReplies?: boolean;
  syncRetweets?: boolean;
}

// Type definition for remove account mutation
interface RemoveAccountMutation {
  mutateAsync: (accountId: string) => Promise<void>;
  isPending: boolean;
}

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation";
import { Switch } from "@/components/ui/switch";
import { trpc } from "@/utils/trpc";

export default function DashboardPage() {
  const { user, isLoaded } = useUser();
  const [replyUrl, setReplyUrl] = useState("");
  const [generatedReply, setGeneratedReply] = useState("");
  const [showAddAccount, setShowAddAccount] = useState(false);
  const [newAccountHandle, setNewAccountHandle] = useState("");

  // Sync settings state
  const [syncSettings, setSyncSettings] = useState({
    syncMentions: true,
    syncUserTweets: false,
    syncReplies: false,
    syncRetweets: true,
  });

  // Account settings expansion state
  const [expandedAccountId, setExpandedAccountId] = useState<string | null>(
    null
  );

  // tRPC queries and utils
  const utils = trpc.useUtils();
  const {
    data: monitoredAccounts,
    isLoading: accountsLoading,
    refetch: refetchAccounts,
  } = trpc.accounts.getMonitored.useQuery();
  const { data: latestMentions, isLoading: mentionsLoading } =
    trpc.mentions.getLatest.useQuery({ limit: 10 });

  // Debug logging
  console.log("🔍 Dashboard: Monitored accounts data:", monitoredAccounts);
  console.log("🔍 Dashboard: Latest mentions data:", latestMentions);

  // tRPC mutations
  const extractTweetMutation = trpc.twitter.extractTweetContent.useMutation();
  const generateReplyMutation = trpc.benji.generateQuickReply.useMutation();
  const addAccountMutation = trpc.accounts.add.useMutation();
  const toggleAccountMutation = trpc.accounts.toggleStatus.useMutation();
  const removeAccountMutation = trpc.accounts.remove.useMutation();
  const updateSyncSettingsMutation =
    trpc.accounts.updateSyncSettings.useMutation();

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-app-background flex items-center justify-center">
        <ShardLoadingAnimation size={80} />
      </div>
    );
  }

  if (!user) {
    redirect("/sign-in");
  }

  const handleAddAccount = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newAccountHandle.trim()) return;

    console.log(
      "🚀 Frontend: Starting account addition for:",
      newAccountHandle.trim()
    );

    try {
      console.log("📡 Frontend: Making tRPC call to add account...");
      console.log("⚙️ Frontend: Sync settings:", syncSettings);
      const result = await addAccountMutation.mutateAsync({
        handle: newAccountHandle.trim(),
        syncSettings,
      });
      console.log("✅ Frontend: Account added successfully:", result);

      // Show enhanced success message with sync information
      let successMessage = result.message || "Account added successfully!";

      // If auto-sync result is available, provide additional feedback
      const syncResult = result.autoSyncResult as SyncResult;
      if (syncResult && typeof syncResult === "object") {
        if (syncResult.success && (syncResult.newMentions ?? 0) > 0) {
          successMessage = `Account added! Found ${syncResult.newMentions} mention${syncResult.newMentions === 1 ? "" : "s"} to monitor.`;
        } else if (syncResult.limitReached) {
          successMessage = `Account added! Mention limit reached (${syncResult.currentCount}/${syncResult.maxAllowed}).`;
        } else if (!syncResult.success && syncResult.error) {
          successMessage = `Account added! (Initial sync: ${syncResult.error})`;
        }
      }

      toast.success(successMessage);
      setNewAccountHandle("");
      setShowAddAccount(false);
      // Reset sync settings to defaults
      setSyncSettings({
        syncMentions: true,
        syncUserTweets: false,
        syncReplies: false,
        syncRetweets: true,
      });

      // Invalidate accounts cache across all pages
      await utils.accounts.getMonitored.invalidate();
      refetchAccounts();
    } catch (error) {
      console.error("❌ Frontend: Error adding account:", error);

      // More detailed error logging
      if (error && typeof error === "object") {
        const apiError = error as APIError;
        console.error("🔴 Frontend: Error details:", {
          message: apiError.message,
          code: apiError.code,
          data: apiError.data,
          shape: apiError.shape,
        });
      }

      // Show more specific error message if available
      const apiError = error as APIError;
      const errorMessage =
        apiError?.message || "Failed to add account. Please try again.";
      toast.error(errorMessage);
    }
  };

  const handleToggleAccount = async (accountId: string, isActive: boolean) => {
    try {
      const result = await toggleAccountMutation.mutateAsync({
        accountId,
        isActive: !isActive,
      });

      toast.success(result.message);

      // Invalidate accounts cache across all pages
      await utils.accounts.getMonitored.invalidate();
      refetchAccounts();
    } catch (error) {
      console.error("Error toggling account:", error);
      const apiError = error as APIError;
      const errorMessage =
        apiError?.message || "Failed to update account status";
      toast.error(errorMessage);
    }
  };

  const handleRemoveAccount = async (accountId: string, handle: string) => {
    if (!confirm(`Are you sure you want to stop monitoring @${handle}?`))
      return;

    try {
      const result = await removeAccountMutation.mutateAsync({ accountId });

      toast.success(result.message);

      // Invalidate accounts cache across all pages
      await utils.accounts.getMonitored.invalidate();
      refetchAccounts();
    } catch (error) {
      console.error("Error removing account:", error);
      const apiError = error as APIError;
      const errorMessage = apiError?.message || "Failed to remove account";
      toast.error(errorMessage);
    }
  };

  const handleQuickReply = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!replyUrl.trim()) return;

    try {
      // First extract tweet content
      const tweetData = await extractTweetMutation.mutateAsync({
        url: replyUrl,
      });

      // Then generate a reply
      const reply = await generateReplyMutation.mutateAsync({
        tweetUrl: replyUrl,
        tweetContent: tweetData.tweet.text,
      });

      setGeneratedReply(reply.response);
      toast.success("Reply generated successfully!");
    } catch (error) {
      console.error("Error generating reply:", error);
      toast.error("Failed to generate reply. Please try again.");
    }
  };

  const handleUseReply = async (responseContent: string) => {
    // Open Twitter with pre-filled reply using URL parameters
    try {
      // Extract tweet ID from the URL
      // Twitter URLs are like: https://twitter.com/username/status/********** or https://x.com/username/status/**********
      const tweetId = replyUrl.match(/\/status\/(\d+)/)?.[1];

      if (!tweetId) {
        toast.error("Could not extract tweet ID from URL.");
        return;
      }

      // Create Twitter compose URL with pre-filled reply
      // Format: https://twitter.com/intent/tweet?in_reply_to=TWEET_ID&text=RESPONSE_TEXT
      const encodedResponse = encodeURIComponent(responseContent);
      const replyIntentUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${encodedResponse}`;

      // Open Twitter with pre-filled reply
      window.open(replyIntentUrl, "_blank", "noopener,noreferrer");
      toast.success("Opening Twitter with pre-filled AI response!");
    } catch (error) {
      console.error("Failed to open Twitter with pre-filled reply:", error);
      // Fallback to copying to clipboard
      try {
        await navigator.clipboard.writeText(responseContent);
        window.open(replyUrl, "_blank", "noopener,noreferrer");
        toast.warning(
          "Opened Twitter normally. AI response copied to clipboard as fallback."
        );
      } catch (clipboardError) {
        toast.error(
          "Failed to open Twitter with pre-filled reply. Please copy manually."
        );
      }
    }
  };

  const handleUpdateSyncSettings = async (
    accountId: string,
    newSyncSettings: typeof syncSettings
  ) => {
    try {
      console.log(
        "🔄 Updating sync settings for account:",
        accountId,
        newSyncSettings
      );
      const result = await updateSyncSettingsMutation.mutateAsync({
        accountId,
        syncSettings: newSyncSettings,
      });

      toast.success(result.message || "Sync settings updated successfully!");

      // Invalidate accounts cache to refresh the data
      await utils.accounts.getMonitored.invalidate();
      refetchAccounts();

      // Close the expanded settings
      setExpandedAccountId(null);
    } catch (error) {
      console.error("Error updating sync settings:", error);
      const apiError = error as APIError;
      const errorMessage =
        apiError?.message || "Failed to update sync settings";
      toast.error(errorMessage);
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours === 1 ? "" : "s"} ago`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} day${days === 1 ? "" : "s"} ago`;
    }
  };

  // Sync Settings Editor Component
  const SyncSettingsEditor = ({
    account,
    onUpdate,
    isUpdating,
    removeAccountMutation,
    refetchAccounts,
  }: {
    account: MonitoredAccount;
    onUpdate: (
      accountId: string,
      settings: typeof syncSettings
    ) => Promise<void>;
    isUpdating: boolean;
    removeAccountMutation: RemoveAccountMutation;
    refetchAccounts: () => void;
  }) => {
    const [localSettings, setLocalSettings] = useState({
      syncMentions: account.syncMentions ?? true,
      syncUserTweets: account.syncUserTweets ?? false,
      syncReplies: account.syncReplies ?? false,
      syncRetweets: account.syncRetweets ?? true,
    });

    const hasChanges =
      localSettings.syncMentions !== (account.syncMentions ?? true) ||
      localSettings.syncUserTweets !== (account.syncUserTweets ?? false) ||
      localSettings.syncReplies !== (account.syncReplies ?? false) ||
      localSettings.syncRetweets !== (account.syncRetweets ?? true);

    const handleSave = () => {
      onUpdate(account.id, localSettings);
    };

    const handleReset = () => {
      setLocalSettings({
        syncMentions: account.syncMentions ?? true,
        syncUserTweets: account.syncUserTweets ?? false,
        syncReplies: account.syncReplies ?? false,
        syncRetweets: account.syncRetweets ?? true,
      });
    };

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-app-headline">
            Sync Settings for @{account.handle}
          </h4>
          <div className="flex items-center gap-2">
            {hasChanges && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                disabled={isUpdating}
                className="text-xs"
              >
                Reset
              </Button>
            )}
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges || isUpdating}
              className="text-xs bg-app-main text-app-secondary hover:bg-app-highlight"
            >
              {isUpdating ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                "Save"
              )}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <div className="flex items-center space-x-3">
            <Switch
              id={`${account.id}-sync-mentions`}
              checked={localSettings.syncMentions}
              onCheckedChange={(checked) =>
                setLocalSettings((prev) => ({ ...prev, syncMentions: checked }))
              }
            />
            <Label
              htmlFor={`${account.id}-sync-mentions`}
              className="text-sm text-app-headline cursor-pointer"
            >
              Mentions
            </Label>
          </div>
          <div className="flex items-center space-x-3">
            <Switch
              id={`${account.id}-sync-user-tweets`}
              checked={localSettings.syncUserTweets}
              onCheckedChange={(checked) =>
                setLocalSettings((prev) => ({
                  ...prev,
                  syncUserTweets: checked,
                }))
              }
            />
            <Label
              htmlFor={`${account.id}-sync-user-tweets`}
              className="text-sm text-app-headline cursor-pointer"
            >
              User's Tweets
            </Label>
          </div>
          <div className="flex items-center space-x-3">
            <Switch
              id={`${account.id}-sync-replies`}
              checked={localSettings.syncReplies}
              onCheckedChange={(checked) =>
                setLocalSettings((prev) => ({ ...prev, syncReplies: checked }))
              }
              disabled={!localSettings.syncUserTweets}
            />
            <Label
              htmlFor={`${account.id}-sync-replies`}
              className={`text-sm cursor-pointer ${!localSettings.syncUserTweets ? "text-app-headline/50" : "text-app-headline"}`}
            >
              Include Replies
            </Label>
          </div>
          <div className="flex items-center space-x-3">
            <Switch
              id={`${account.id}-sync-retweets`}
              checked={localSettings.syncRetweets}
              onCheckedChange={(checked) =>
                setLocalSettings((prev) => ({ ...prev, syncRetweets: checked }))
              }
              disabled={!localSettings.syncUserTweets}
            />
            <Label
              htmlFor={`${account.id}-sync-retweets`}
              className={`text-sm cursor-pointer ${!localSettings.syncUserTweets ? "text-app-headline/50" : "text-app-headline"}`}
            >
              Include Retweets
            </Label>
          </div>
        </div>

        <div className="text-xs text-app-headline/60">
          💡 Mentions track when others mention this account. User's Tweets
          track posts made by this account.
        </div>

        {/* Delete Account Section */}
        <div className="border-t border-app-stroke/30 pt-4 mt-4">
          <div className="flex items-center justify-between">
            <div>
              <h5 className="text-sm font-medium text-app-headline">
                Remove Account
              </h5>
              <p className="text-xs text-app-headline/60 mt-1">
                Stop monitoring this account and remove it from your dashboard.
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (
                  confirm(
                    `Are you sure you want to stop monitoring @${account.handle}? This will remove the account from your dashboard.`
                  )
                ) {
                  // Use the same handleRemoveAccount function from the parent
                  const removeAccount = async () => {
                    try {
                      await removeAccountMutation.mutateAsync(account.id);
                      toast.success(`Stopped monitoring @${account.handle}`);
                      refetchAccounts();
                    } catch (error: unknown) {
                      console.error("Error removing account:", error);
                      const apiError = error as APIError;
                      const errorMessage =
                        apiError?.message || "Failed to remove account";
                      toast.error(errorMessage);
                    }
                  };
                  removeAccount();
                }
              }}
              disabled={removeAccountMutation.isPending}
              className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
            >
              {removeAccountMutation.isPending ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <>
                  <Trash2 className="w-3 h-3 mr-1" />
                  Remove
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen p-3 sm:p-4 md:p-6 lg:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-6 sm:mb-8">
        <h1 className="text-[clamp(1.75rem,5vw,3rem)] font-bold tracking-wider text-app-headline">
          BUDDYCHIP DASHBOARD
        </h1>
      </header>
      <AuthenticatedNavbar currentPage="dashboard" />

      <main className="space-y-6 sm:space-y-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Monitored Accounts Section */}
          <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-base sm:text-lg font-semibold text-app-headline">
                  MONITORED ACCOUNTS
                </h2>
                <Button
                  size="sm"
                  onClick={() => setShowAddAccount(!showAddAccount)}
                  className={`
                    relative overflow-hidden rounded-lg px-3 py-2 text-xs sm:text-sm font-medium min-h-[44px] min-w-[44px]
                    transition-all duration-300 ease-in-out transform
                    ${
                      showAddAccount
                        ? "bg-purple-600 text-white shadow-lg scale-105 border-purple-600 hover:bg-purple-700 hover:border-purple-700"
                        : "bg-white text-gray-700 hover:bg-purple-600 hover:text-white border-gray-300 hover:border-purple-600"
                    }
                    hover:shadow-md hover:scale-105
                    focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
                    group
                  `}
                >
                  <div className="flex items-center space-x-1">
                    <Plus
                      className={`w-4 h-4 transition-transform duration-300 ${showAddAccount ? "rotate-45" : "group-hover:scale-110"}`}
                    />
                    <span className="hidden sm:inline">Add</span>
                  </div>
                </Button>
              </div>

              {/* Add Account Form */}
              {showAddAccount && (
                <div className="mb-4 p-3 sm:p-4 bg-app-background rounded-md border border-app-stroke/50">
                  <form onSubmit={handleAddAccount} className="space-y-4">
                    {/* Twitter Handle Input */}
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                      <Input
                        type="text"
                        placeholder="Enter Twitter handle (e.g., @elonmusk)"
                        value={newAccountHandle}
                        onChange={(e) => setNewAccountHandle(e.target.value)}
                        className="flex-1 bg-app-card border-app-stroke text-app-headline placeholder:text-app-headline placeholder:opacity-60 focus:border-app-main min-h-[44px] text-base sm:text-sm"
                      />
                      <Button
                        type="submit"
                        size="sm"
                        disabled={
                          addAccountMutation.isPending ||
                          !newAccountHandle.trim()
                        }
                        className="bg-app-main text-app-secondary hover:bg-app-highlight disabled:bg-app-main/50 min-h-[44px] px-6 sm:px-4"
                      >
                        {addAccountMutation.isPending ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          "Add Account"
                        )}
                      </Button>
                    </div>

                    {/* Sync Settings */}
                    <div className="space-y-3">
                      <div className="text-sm font-medium text-app-headline">
                        Content to Monitor
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div className="flex items-center space-x-3">
                          <Switch
                            id="sync-mentions"
                            checked={syncSettings.syncMentions}
                            onCheckedChange={(checked) =>
                              setSyncSettings((prev) => ({
                                ...prev,
                                syncMentions: checked,
                              }))
                            }
                          />
                          <Label
                            htmlFor="sync-mentions"
                            className="text-sm text-app-headline cursor-pointer"
                          >
                            Mentions
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Switch
                            id="sync-user-tweets"
                            checked={syncSettings.syncUserTweets}
                            onCheckedChange={(checked) =>
                              setSyncSettings((prev) => ({
                                ...prev,
                                syncUserTweets: checked,
                              }))
                            }
                          />
                          <Label
                            htmlFor="sync-user-tweets"
                            className="text-sm text-app-headline cursor-pointer"
                          >
                            User's Tweets
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Switch
                            id="sync-replies"
                            checked={syncSettings.syncReplies}
                            onCheckedChange={(checked) =>
                              setSyncSettings((prev) => ({
                                ...prev,
                                syncReplies: checked,
                              }))
                            }
                            disabled={!syncSettings.syncUserTweets}
                          />
                          <Label
                            htmlFor="sync-replies"
                            className={`text-sm cursor-pointer ${!syncSettings.syncUserTweets ? "text-app-headline/50" : "text-app-headline"}`}
                          >
                            Include Replies
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Switch
                            id="sync-retweets"
                            checked={syncSettings.syncRetweets}
                            onCheckedChange={(checked) =>
                              setSyncSettings((prev) => ({
                                ...prev,
                                syncRetweets: checked,
                              }))
                            }
                            disabled={!syncSettings.syncUserTweets}
                          />
                          <Label
                            htmlFor="sync-retweets"
                            className={`text-sm cursor-pointer ${!syncSettings.syncUserTweets ? "text-app-headline/50" : "text-app-headline"}`}
                          >
                            Include Retweets
                          </Label>
                        </div>
                      </div>
                      <div className="text-xs text-app-headline/60">
                        💡 Mentions track when others mention this account.
                        User's Tweets track posts made by this account.
                      </div>
                    </div>
                  </form>
                </div>
              )}

              {accountsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <ShardLoadingAnimation size={64} />
                </div>
              ) : (
                <div className="space-y-3">
                  {!monitoredAccounts?.accounts ||
                  monitoredAccounts.accounts.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="mb-3">
                        <Lightbulb className="w-12 h-12 text-app-main/30 mx-auto" />
                      </div>
                      <p className="text-sm text-app-headline opacity-60 mb-2">
                        No monitored accounts yet
                      </p>
                      <p className="text-xs text-app-headline opacity-40">
                        Add Twitter accounts to start monitoring mentions
                      </p>
                    </div>
                  ) : (
                    monitoredAccounts.accounts.map(
                      (account: MonitoredAccount) => (
                        <div
                          key={account.id}
                          className="group bg-app-background/50 rounded-lg border border-app-stroke/30 hover:border-app-stroke/60 transition-all duration-200"
                        >
                          {/* Main Account Card */}
                          <div className="p-3 sm:p-4">
                            <div className="flex items-center justify-between gap-3">
                              <div className="flex items-center space-x-3 flex-1 min-w-0">
                                <Avatar className="h-10 w-10 sm:h-12 sm:w-12 flex-shrink-0">
                                  <AvatarImage
                                    src={
                                      account.avatar ||
                                      `https://unavatar.io/twitter/${account.handle}`
                                    }
                                    alt={account.handle}
                                  />
                                  <AvatarFallback className="bg-app-main/10 text-app-main text-sm font-medium">
                                    {account.handle.slice(0, 2).toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>

                                <div className="flex-1 min-w-0">
                                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                                    <h4 className="text-sm sm:text-base font-medium text-app-headline truncate">
                                      {account.displayName || account.handle}
                                    </h4>
                                    <Badge
                                      variant={
                                        account.isActive
                                          ? "default"
                                          : "secondary"
                                      }
                                      className={`text-xs px-2 py-0.5 w-fit ${
                                        account.isActive
                                          ? "bg-green-100 text-green-800 border-green-200"
                                          : "bg-gray-100 text-gray-600 border-gray-200"
                                      }`}
                                    >
                                      {account.isActive ? "Active" : "Paused"}
                                    </Badge>
                                  </div>
                                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 mt-1">
                                    <p className="text-xs sm:text-sm text-app-headline/70">
                                      @{account.handle}
                                    </p>
                                    <p className="text-xs text-app-headline/60">
                                      {account.mentionsCount} mentions
                                    </p>
                                    {/* Sync Status Indicators */}
                                    <div className="flex items-center gap-1">
                                      {account.syncMentions && (
                                        <Badge
                                          variant="outline"
                                          className="text-xs px-1 py-0 bg-blue-50 text-blue-700 border-blue-200"
                                        >
                                          M
                                        </Badge>
                                      )}
                                      {account.syncUserTweets && (
                                        <Badge
                                          variant="outline"
                                          className="text-xs px-1 py-0 bg-purple-50 text-purple-700 border-purple-200"
                                        >
                                          T
                                        </Badge>
                                      )}
                                      {account.syncReplies && (
                                        <Badge
                                          variant="outline"
                                          className="text-xs px-1 py-0 bg-orange-50 text-orange-700 border-orange-200"
                                        >
                                          R
                                        </Badge>
                                      )}
                                      {account.syncRetweets && (
                                        <Badge
                                          variant="outline"
                                          className="text-xs px-1 py-0 bg-green-50 text-green-700 border-green-200"
                                        >
                                          RT
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-3 flex-shrink-0">
                                <Switch
                                  checked={account.isActive}
                                  onCheckedChange={() =>
                                    handleToggleAccount(
                                      account.id,
                                      account.isActive
                                    )
                                  }
                                  disabled={toggleAccountMutation.isPending}
                                  className="data-[state=checked]:bg-green-600 data-[state=unchecked]:bg-gray-300"
                                />

                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    setExpandedAccountId(
                                      expandedAccountId === account.id
                                        ? null
                                        : account.id
                                    )
                                  }
                                  className="h-8 w-8 sm:h-9 sm:w-9 p-0 text-app-headline/50 hover:text-app-main hover:bg-app-main/10 transition-colors min-h-[44px] min-w-[44px]"
                                >
                                  <Settings className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>

                          {/* Expandable Sync Settings */}
                          {expandedAccountId === account.id && (
                            <div className="border-t border-app-stroke/30 p-3 sm:p-4 bg-app-background/30">
                              <SyncSettingsEditor
                                account={account}
                                onUpdate={handleUpdateSyncSettings}
                                isUpdating={
                                  updateSyncSettingsMutation.isPending
                                }
                                removeAccountMutation={
                                  removeAccountMutation as unknown as RemoveAccountMutation
                                }
                                refetchAccounts={refetchAccounts}
                              />
                            </div>
                          )}
                        </div>
                      )
                    )
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Latest 10 Mentions Section */}
          <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
            <CardContent className="p-4 sm:p-6">
              <h2 className="text-base sm:text-lg font-semibold mb-4 text-app-headline">
                LATEST 10 MENTIONS
              </h2>
              {mentionsLoading ? (
                <div className="flex items-center justify-center py-4">
                  <ShardLoadingAnimation size={64} />
                </div>
              ) : (
                <div className="space-y-3 max-h-80 sm:max-h-96 overflow-y-auto">
                  {!latestMentions?.mentions ||
                  latestMentions.mentions.length === 0 ? (
                    <div className="text-sm text-app-headline opacity-60 text-center py-8">
                      No mentions found. Start monitoring accounts to see
                      mentions here!
                    </div>
                  ) : (
                    latestMentions.mentions.map((mention: any) => (
                      <div
                        key={mention.id}
                        className="p-3 sm:p-4 bg-app-background rounded-md border border-app-stroke/50"
                      >
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                          <span className="text-sm sm:text-base font-medium text-app-headline truncate">
                            {mention.authorHandle}
                          </span>
                          <div className="flex items-center space-x-2 flex-shrink-0">
                            <span className="text-xs sm:text-sm text-app-headline opacity-70">
                              {formatTimeAgo(new Date(mention.createdAt))}
                            </span>
                            <a
                              href={mention.tweetUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:text-app-main transition-colors p-1 min-h-[44px] min-w-[44px] flex items-center justify-center"
                            >
                              <ExternalLink className="w-4 h-4 text-app-headline opacity-50 hover:opacity-100" />
                            </a>
                          </div>
                        </div>
                        <p className="text-xs sm:text-sm text-app-headline opacity-90 leading-relaxed">
                          {mention.content}
                        </p>
                        {mention.bullishScore && (
                          <div className="mt-2">
                            <span className="text-xs sm:text-sm text-app-main font-medium">
                              Bullish Score: {mention.bullishScore}/100
                            </span>
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Reply Section */}
        <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
          <CardContent className="p-4 sm:p-6">
            <h2 className="text-lg sm:text-xl font-semibold mb-3 text-center text-app-headline">
              QUICK REPLY
            </h2>
            <p className="text-sm sm:text-base text-app-headline opacity-90 text-center leading-relaxed max-w-2xl mx-auto mb-6">
              Paste a link from x.com or twitter.com, we will get info about the
              specific post, and uses the{" "}
              <span className="font-semibold text-app-main">
                ANSWER-ENGINE AGENT
              </span>{" "}
              to craft a response.
            </p>

            <form
              onSubmit={handleQuickReply}
              className="max-w-2xl mx-auto space-y-4"
            >
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
                <Input
                  type="url"
                  placeholder="Paste X.com or Twitter.com link here..."
                  value={replyUrl}
                  onChange={(e) => setReplyUrl(e.target.value)}
                  className="flex-1 bg-app-background border-app-stroke text-app-headline placeholder:text-app-headline placeholder:opacity-60 focus:border-app-main min-h-[44px] text-base sm:text-sm"
                />
                <Button
                  type="submit"
                  disabled={
                    extractTweetMutation.isPending ||
                    generateReplyMutation.isPending ||
                    !replyUrl.trim()
                  }
                  className="bg-app-main text-app-secondary hover:bg-app-highlight disabled:bg-app-main/50 min-h-[44px] px-6 sm:px-4"
                >
                  {extractTweetMutation.isPending ||
                  generateReplyMutation.isPending ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      <span className="hidden sm:inline">Generating...</span>
                      <span className="sm:hidden">...</span>
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      <span className="hidden sm:inline">Generate</span>
                      <span className="sm:hidden">Go</span>
                    </>
                  )}
                </Button>
              </div>
            </form>

            {generatedReply && (
              <div className="mt-6 max-w-2xl mx-auto">
                <h3 className="text-sm sm:text-base font-semibold text-app-headline mb-2">
                  Generated Reply:
                </h3>
                <div className="p-4 bg-app-background rounded-md border border-app-stroke">
                  <p className="text-app-headline text-sm sm:text-base leading-relaxed">
                    {generatedReply}
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row justify-end mt-3 gap-2 sm:gap-2 sm:space-x-0">
                  <Button
                    size="sm"
                    variant="outline"
                    className="border-app-stroke text-app-headline hover:bg-app-headline hover:text-app-background min-h-[44px] order-2 sm:order-1"
                    onClick={() => setGeneratedReply("")}
                  >
                    Clear
                  </Button>
                  <Button
                    size="sm"
                    className="bg-app-main text-app-secondary hover:bg-app-highlight min-h-[44px] order-1 sm:order-2"
                    onClick={() => handleUseReply(generatedReply)}
                  >
                    Use Reply
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}

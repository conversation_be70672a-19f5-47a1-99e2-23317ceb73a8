#!/usr/bin/env node

/**
 * Simple test script to verify Telegram bot is working
 * Run with: node test-telegram-bot.js
 */

const TelegramBot = require('node-telegram-bot-api');

// Load environment variables
require('dotenv').config({ path: './apps/web/.env' });

const token = process.env.TELEGRAM_BOT_TOKEN;

if (!token) {
  console.error('❌ TELEGRAM_BOT_TOKEN not found in environment variables');
  process.exit(1);
}

console.log('🤖 Testing Telegram bot connection...');
console.log('📱 Bot token:', token.substring(0, 10) + '...');

// Create bot instance
const bot = new TelegramBot(token, { polling: true });

// Test bot info
bot.getMe()
  .then((botInfo) => {
    console.log('✅ Bot connection successful!');
    console.log('🤖 Bot info:', {
      id: botInfo.id,
      username: botInfo.username,
      first_name: botInfo.first_name,
      can_join_groups: botInfo.can_join_groups,
      can_read_all_group_messages: botInfo.can_read_all_group_messages,
      supports_inline_queries: botInfo.supports_inline_queries
    });
  })
  .catch((error) => {
    console.error('❌ Bot connection failed:', error.message);
    process.exit(1);
  });

// Listen for messages
bot.on('message', (msg) => {
  console.log('📨 Received message:', {
    chat_id: msg.chat.id,
    from: msg.from.username || msg.from.first_name,
    text: msg.text,
    date: new Date(msg.date * 1000).toISOString()
  });

  // Send a simple response
  bot.sendMessage(msg.chat.id, '🤖 Test response: Bot is working! This is a test message from the development environment.');
});

// Handle errors
bot.on('error', (error) => {
  console.error('❌ Bot error:', error);
});

// Handle polling errors
bot.on('polling_error', (error) => {
  console.error('❌ Polling error:', error);
});

console.log('🔄 Bot is now listening for messages...');
console.log('💬 Send a message to your bot to test it!');
console.log('🛑 Press Ctrl+C to stop');

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping bot...');
  bot.stopPolling();
  process.exit(0);
});
